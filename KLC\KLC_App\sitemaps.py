from django.contrib.sitemaps import Sitemap, GenericSitemap
from django.contrib.sitemaps import Sitemap
from django.urls import reverse
from django.views.generic import TemplateView


class StaticViewSitemap(Sitemap):
    """Sitemap for static pages - using hardcoded URLs to avoid reverse() issues"""

    def items(self):
        return [
            {'url': '/', 'priority': 1.0, 'changefreq': 'weekly'},
            {'url': '/index/', 'priority': 0.9, 'changefreq': 'weekly'},
            {'url': '/check_person_id/', 'priority': 0.8, 'changefreq': 'monthly'},
            {'url': '/services/', 'priority': 0.9, 'changefreq': 'weekly'},
            {'url': '/about_developers/', 'priority': 0.3, 'changefreq': 'yearly'},
            {'url': '/admin_login', 'priority': 0.2, 'changefreq': 'yearly'},
            {'url': '/news/', 'priority': 0.8, 'changefreq': 'daily'},
        ]

    def location(self, item):
        return item['url']

    def priority(self, item):
        return item['priority']

    def changefreq(self, item):
        return item['changefreq']


# Sitemap dictionary for Django
sitemaps = {
    'static': StaticViewSitemap,
}